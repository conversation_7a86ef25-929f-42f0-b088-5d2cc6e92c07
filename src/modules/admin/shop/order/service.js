const Order = require('../../../../models/Order');
const Payment = require('../../../../models/Payment');
const Transaction = require('../../../../models/Transaction');
const ProductShipment = require('../../../../models/ProductShipment');
const { throwBadRequestError, throwNotFoundError } = require('../../../../errors');
const { messages } = require('../../../../messages');
const { orderStatusValue, paymentStatusValue, transactionTypeValue, transactionStatusValue, shipmentStatusValue } = require('../../../../constants/dbEnums');
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');
const { transformTranslatedFields } = require('../../../../utils/localizer');
const moment = require('moment');

/**
 * Get all orders with pagination and filters
 */
const getAllOrders = async ({ page, limit, status, search, startDate, endDate, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const query = {
    orderStatus: { $ne: orderStatusValue.PENDING }
  };

  if (status) {
    query.orderStatus = status;
  }

  const language = { code: 'en' };

  if (search) {
    query.$or = [
      { orderNumber: { $regex: search, $options: 'i' } },
      { [`shippingAddress.name.${language.code}`]: { $regex: search, $options: 'i' } },
      { 'shippingAddress.phone': { $regex: search, $options: 'i' } },
      { [`items.name.${language.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  if (startDate && endDate) {
    query.createdAt = {
      $gte: moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  } else if (startDate) {
    query.createdAt = { $gte: moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z' };
  } else if (endDate) {
    query.createdAt = { $lte: moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z' };
  }

  const orders = await Order.find(query)
    .populate('user', 'firstName lastName email phoneNumber')
    .populate('shipments')
    .select('-stockUpdates -variantStockUpdates -stockReduced')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit).lean();

  const total = await Order.countDocuments(query);

  return {
    orders: await transformTranslatedFields(orders, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get orders dashboard data
 */
const getOrdersDashboard = async () => {
  // Get counts by status
  const statusCounts = await Order.aggregate([
    {
      $group: {
        _id: '$orderStatus',
        count: { $sum: 1 }
      }
    }
  ]);

  // Get total revenue
  const revenue = await Order.aggregate([
    {
      $match: {
        paymentStatus: paymentStatusValue.COMPLETED
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$total' }
      }
    }
  ]);

  // Get recent orders
  let recentOrders = await Order.find()
    .populate('user', 'firstName lastName')
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .limit(5).lean();

  // Format status counts
  const formattedStatusCounts = {};

  statusCounts.forEach(item => {
    formattedStatusCounts[item._id] = item.count;
  });

  const language = { code: 'en' };

  recentOrders = await transformTranslatedFields(recentOrders, language.code);

  return {
    statusCounts: formattedStatusCounts,
    totalRevenue: revenue.length > 0 ? revenue[0].total : 0,
    recentOrders
  };
};

/**
 * Get order by ID
 */
const getOrderById = async (orderId) => {
  // Check if order ID is valid
  if (!mongoose.Types.ObjectId.isValid(orderId)) {
    throwBadRequestError(messages.INVALID_ORDER_ID);
  }

  const order = await Order.findById(orderId)
    .populate('user', 'name email phoneNumber')
    .populate({
      path: 'items.product'
    })
    .populate({
      path: 'items.variant'
    })
    .lean();

  if (!order) {
    throwNotFoundError(messages.ORDER_NOT_FOUND);
  }

  await Promise.all(
    order.items.map(async item => {
      if (item.vendor && item.vendorModel) {
        const Model = mongoose.model(item.vendorModel);
        const vendorDoc = await Model.findById(item.vendor).lean();

        item.vendor = vendorDoc;
      }
    })
  );

  const language = { code: 'en' };

  // Get payment info
  const payment = await Payment.findOne({ order: orderId });

  // Get shipments info if available
  const shipments = await ProductShipment.find({ order: orderId })
    .populate('orderItems.product orderItems.variant vendor');

  order.shipments = shipments;

  // Get transactions
  const transactions = await Transaction.find({ order: orderId })
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 });

  return {
    order: await transformTranslatedFields(order, language.code),
    payment,
    transactions
  };
};

/**
 * Update order status
 */
const updateOrderStatus = async ({ orderId, status, notes }) => {
  // Check if order ID is valid
  if (!mongoose.Types.ObjectId.isValid(orderId)) {
    throwBadRequestError(messages.INVALID_ORDER_ID);
  }

  const order = await Order.findById(orderId);

  if (!order) {
    throwBadRequestError(messages.ORDER_NOT_FOUND);
  }

  // Validate status transition
  validateStatusTransition(order.orderStatus, status);

  // Start a transaction
  const session = await mongoose.startSession();

  session.startTransaction();

  try {
    // Update order status
    order.orderStatus = status;
    if (notes) {
      order.notes = notes;
    }

    // Update timestamps for specific statuses
    if (status === orderStatusValue.DELIVERED) {
      order.deliveredAt = new Date();
    } else if (status === orderStatusValue.CANCELLED) {
      order.cancelledAt = new Date();
      order.cancelReason = notes || 'Cancelled by admin';
    }

    await order.save({ session });

    // Update shipments status if needed
    if (status === orderStatusValue.PARTIALLY_SHIPPED || status === orderStatusValue.SHIPPED || status === orderStatusValue.DELIVERED) {
      const shipments = await ProductShipment.find({ order: orderId });

      if (shipments && shipments.length > 0) {
        for (const shipment of shipments) {
          if (status === orderStatusValue.PARTIALLY_SHIPPED) {
            shipment.status = shipmentStatusValue.SHIPPED;
          } else if (status === orderStatusValue.SHIPPED) {
            shipment.status = shipmentStatusValue.SHIPPED;
          } else if (status === orderStatusValue.DELIVERED) {
            shipment.status = shipmentStatusValue.DELIVERED;
            shipment.deliveryDate = new Date();
          }

          await shipment.save({ session });
        }
      }
    }

    // Commit transaction
    await session.commitTransaction();
    session.endSession();

    return order;
  } catch (error) {
    // Abort transaction on error
    await session.abortTransaction();
    session.endSession();
    throw error;
  }
};

/**
 * Refund order
 */
const refundOrder = async ({ orderId, amount, reason }) => {
  // Check if order ID is valid
  if (!mongoose.Types.ObjectId.isValid(orderId)) {
    throwBadRequestError(messages.INVALID_ORDER_ID);
  }

  const order = await Order.findById(orderId);

  if (!order) {
    throwBadRequestError(messages.ORDER_NOT_FOUND);
  }

  // Check if order can be refunded
  if (order.paymentStatus !== paymentStatusValue.COMPLETED) {
    throwBadRequestError(messages.ORDER_CANNOT_BE_REFUNDED);
  }

  // Check if refund amount is valid
  if (amount > order.total) {
    throwBadRequestError(messages.REFUND_AMOUNT_EXCEEDS_ORDER_TOTAL);
  }

  // Get payment record
  const payment = await Payment.findOne({ order: orderId });

  if (!payment) {
    throwBadRequestError(messages.PAYMENT_RECORD_NOT_FOUND);
  }

  // Start a transaction
  const session = await mongoose.startSession();

  session.startTransaction();

  try {
    // Update payment record
    payment.refundAmount = amount;
    payment.refundReason = reason;
    payment.refundedAt = new Date();
    payment.status = amount === order.total ?
      paymentStatusValue.REFUNDED :
      paymentStatusValue.PARTIALLY_REFUNDED;

    await payment.save({ session });

    // Update order status
    order.paymentStatus = payment.status;
    if (amount === order.total) {
      order.orderStatus = orderStatusValue.REFUNDED;
    }

    await order.save({ session });

    // Create refund transaction
    const transaction = new Transaction({
      transactionId: `REF-${uuidv4()}`,
      order: orderId,
      payment: payment._id,
      user: order.user,
      amount: amount,
      type: transactionTypeValue.REFUND,
      status: transactionStatusValue.COMPLETED,
      notes: reason
    });

    await transaction.save({ session });

    // Commit transaction
    await session.commitTransaction();
    session.endSession();

    return { success: true };
  } catch (error) {
    // Abort transaction on error
    await session.abortTransaction();
    session.endSession();
    throw error;
  }
};

/**
 * Ship order
 */
const shipOrder = async ({ orderId, trackingNumber, shippingProvider, estimatedDelivery }) => {
  // Check if order ID is valid
  if (!mongoose.Types.ObjectId.isValid(orderId)) {
    throwBadRequestError(messages.INVALID_ORDER_ID);
  }

  const order = await Order.findById(orderId);

  if (!order) {
    throwBadRequestError(messages.ORDER_NOT_FOUND);
  }

  // Check if order can be shipped
  if (order.orderStatus !== orderStatusValue.PROCESSING) {
    throwBadRequestError(messages.ORDER_CANNOT_BE_SHIPPED);
  }

  // Start a transaction
  const session = await mongoose.startSession();

  session.startTransaction();

  try {
    // Update order status
    order.orderStatus = orderStatusValue.SHIPPED;
    order.trackingNumber = trackingNumber;
    order.shippingProvider = shippingProvider;
    order.estimatedDelivery = estimatedDelivery ? new Date(estimatedDelivery) : null;

    await order.save({ session });

    // Create a shipment for the order if none exists
    const existingShipments = await ProductShipment.find({ order: orderId });

    if (existingShipments.length === 0) {
      // If no shipments exist, create one for all items in the order
      const newShipment = new ProductShipment({
        order: orderId,
        orderItems: order.items.map(item => ({
          orderItem: item._id,
          product: item.product,
          variant: item.variant || null,
          quantity: item.quantity,
          price: item.price,
          subtotal: item.subtotal,
          name: item.name,
          variantAttributes: item.variantAttributes || {}
        })),
        vendor: order.items[0].vendor, // Use the first item's vendor
        vendorModel: order.items[0].vendorModel,
        trackingId: trackingNumber,
        courier: {
          courierName: shippingProvider
        },
        status: shipmentStatusValue.SHIPPED,
        estimatedDeliveryDate: estimatedDelivery ? new Date(estimatedDelivery) : null,
        deliveryAddress: order.shippingAddress
      });

      await newShipment.save({ session });
    } else {
      // Update existing shipments
      for (const shipment of existingShipments) {
        shipment.trackingId = trackingNumber;
        shipment.courier = {
          courierName: shippingProvider,
          courierId: shipment.courier?.courierId
        };
        shipment.status = shipmentStatusValue.SHIPPED;
        shipment.estimatedDeliveryDate = estimatedDelivery ? new Date(estimatedDelivery) : null;

        await shipment.save({ session });
      }
    }

    // Commit transaction
    await session.commitTransaction();
    session.endSession();

    return { success: true };
  } catch (error) {
    // Abort transaction on error
    await session.abortTransaction();
    session.endSession();
    throw error;
  }
};

/**
 * Helper function to validate order status transitions
 */
const validateStatusTransition = (currentStatus, newStatus) => {
  const validTransitions = {
    [orderStatusValue.PENDING]: [ orderStatusValue.PROCESSING, orderStatusValue.CANCELLED ],
    [orderStatusValue.PROCESSING]: [ orderStatusValue.PARTIALLY_SHIPPED, orderStatusValue.SHIPPED, orderStatusValue.CANCELLED ],
    [orderStatusValue.PARTIALLY_SHIPPED]: [ orderStatusValue.SHIPPED, orderStatusValue.DELIVERED, orderStatusValue.CANCELLED ],
    [orderStatusValue.SHIPPED]: [ orderStatusValue.DELIVERED, orderStatusValue.RETURNED ],
    [orderStatusValue.DELIVERED]: [ orderStatusValue.RETURNED, orderStatusValue.REFUNDED ],
    [orderStatusValue.CANCELLED]: [],
    [orderStatusValue.RETURNED]: [ orderStatusValue.REFUNDED ],
    [orderStatusValue.REFUNDED]: []
  };

  if (!validTransitions[currentStatus].includes(newStatus)) {
    throwBadRequestError(`Cannot change order status from ${currentStatus} to ${newStatus}`);
  }
};

module.exports = {
  getAllOrders,
  getOrdersDashboard,
  getOrderById,
  updateOrderStatus,
  refundOrder,
  shipOrder
};
